# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.xihuan: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 500
    # 密码锁定时间（默认10分钟）
    lockTime: 1

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: B0pL6sK9vXw2z\$rC%qWnZr4u7x!A*D(G+MbQeThWmZq4t6w9z\$C&F)J@NcRfUjXn
  # 令牌有效期（默认30分钟）
  expireTime: 3000

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.xihuan.**.domain,com.xihuan.common.core.domain.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 微信小程序配置
wx:
  app:
    appid: wx8df6fcafd17d7348
    secret: ed9628cbab3b02daf1f9624d32dbcf95
    grant_type: authorization_code
    session_url: https://api.weixin.qq.com/sns/jscode2session
  # 微信支付配置
  pay:
    # 微信支付商户号
    mch-id: 1234567890
    # 微信支付商户API密钥（V2版本）
    mch-key: your_mch_key_here_32_characters_long
    # 微信支付API V3密钥
    api-v3-key: your_api_v3_key_here_32_characters_long
    # 微信支付回调地址
    notify-url: https://dev.yourdomain.com/miniapp/payment/notify/pay
    # 微信小程序APPID（与上面的appid保持一致）
    app-id: wx8df6fcafd17d7348
    # 是否使用沙箱环境
    use-sandbox: true
    # 是否使用V3版本接口
    use-v3: true
    # 私钥路径（V3版本需要）
    private-key-path: classpath:cert/apiclient_key.pem
    # 平台证书序列号（V3版本需要）
    cert-serial-no: your_cert_serial_no_here
    # 商户证书路径
    mch-cert-path: classpath:cert/apiclient_cert.p12
    # 商户证书密码（通常是商户号）
    mch-cert-password: 1234567890
    # 超时时间，单位分钟
    timeout-minutes: 5

# 阿里云数据存储配置
aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5tLK5JNrjSDJxMGxUWtT
    access-key-secret: ******************************
    bucket-name: xihuanxinli
    url-expire: 3600 # URL有效期(秒)
    # domain: https://cdn.yourdomain.com  # 可选
    # prefix: upload/  # 可选
  # 阿里云短信服务配置
  sms:
    accessKeyId: LTAI5tLK5JNrjSDJxMGxUWtT
    accessKeySecret: ******************************
    templateCode: SMS_230270626
    signName: 熙桓心理
