package com.xihuan.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.xihuan.common.config.WxPayConfig;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.wechat.WxPayUtils;
import com.xihuan.system.domain.dto.WxPayDTO;
import com.xihuan.system.service.IPsyCourseOrderService;
import com.xihuan.system.service.IPsyMeditationOrderService;
import com.xihuan.system.service.IWxPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.Map;

/**
 * 微信支付服务实现类
 */
@Service
public class WxPayServiceImpl implements IWxPayService {
    private static final Logger log = LoggerFactory.getLogger(WxPayServiceImpl.class);

    @Autowired
    private WxPayConfig wxPayConfig;

    @Autowired
    private WxPayUtils wxPayUtils;

    @Autowired
    private IPsyCourseOrderService courseOrderService;

    @Autowired
    private IPsyMeditationOrderService meditationOrderService;

    @Override
    public WxPayDTO.PayOrderResponse createPayOrder(WxPayDTO.CreatePayOrderRequest request) {
        try {
            log.info("创建微信支付订单，订单号: {}, 金额: {}", request.getOrderNo(), request.getAmount());

            // 将金额转换为分
            int totalFee = request.getAmount().multiply(new BigDecimal("100")).intValue();

            // 调用微信支付统一下单接口
            Map<String, Object> payParams = wxPayUtils.createOrder(
                    request.getOrderNo(),
                    totalFee,
                    request.getDescription(),
                    request.getOpenid(),
                    request.getClientIp()
            );

            // 构建响应
            WxPayDTO.PayOrderResponse response = new WxPayDTO.PayOrderResponse();
            response.setOrderNo(request.getOrderNo());
            response.setAmount(request.getAmount());

            // 构建小程序调起支付参数
            WxPayDTO.PayParams payParamsDto = new WxPayDTO.PayParams();
            payParamsDto.setAppId((String) payParams.get("appId"));
            payParamsDto.setTimeStamp((String) payParams.get("timeStamp"));
            payParamsDto.setNonceStr((String) payParams.get("nonceStr"));
            payParamsDto.setPackageValue((String) payParams.get("package"));
            payParamsDto.setSignType((String) payParams.get("signType"));
            payParamsDto.setPaySign((String) payParams.get("paySign"));

            String prepayId = "wx" + System.currentTimeMillis();
            response.setPayParams(payParamsDto);

            // 设置过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(wxPayConfig.getTimeoutMinutes());
            response.setExpireTime(expireTime.format(DateTimeFormatter.ISO_DATE_TIME));
            response.setPrepayId(prepayId);
            log.info("微信支付订单创建成功，订单号: {}", request.getOrderNo());
            return response;

        } catch (Exception e) {
            log.error("创建微信支付订单失败，订单号: {}", request.getOrderNo(), e);
            throw new ServiceException("创建支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public WxPayDTO.QueryOrderResponse queryOrder(WxPayDTO.QueryOrderRequest request) {
        try {
            log.info("查询微信支付订单，商户订单号: {}, 微信订单号: {}", request.getOutTradeNo(), request.getTransactionId());

            // TODO: 实现查询订单逻辑
            // 这里需要调用微信支付查询订单接口
            
            WxPayDTO.QueryOrderResponse response = new WxPayDTO.QueryOrderResponse();
            // 模拟返回数据
            response.setOutTradeNo(request.getOutTradeNo());
            response.setTradeState("SUCCESS");
            response.setTradeStateDesc("支付成功");

            return response;

        } catch (Exception e) {
            log.error("查询微信支付订单失败", e);
            throw new ServiceException("查询支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public WxPayDTO.RefundResponse refund(WxPayDTO.RefundRequest request) {
        try {
            log.info("申请微信支付退款，订单号: {}, 退款单号: {}, 退款金额: {}", 
                    request.getOutTradeNo(), request.getOutRefundNo(), request.getRefundAmount());

            // TODO: 实现退款逻辑
            // 这里需要调用微信支付申请退款接口

            WxPayDTO.RefundResponse response = new WxPayDTO.RefundResponse();
            // 模拟返回数据
            response.setOutRefundNo(request.getOutRefundNo());
            response.setOutTradeNo(request.getOutTradeNo());
            response.setStatus("SUCCESS");

            return response;

        } catch (Exception e) {
            log.error("申请微信支付退款失败", e);
            throw new ServiceException("申请退款失败: " + e.getMessage());
        }
    }

    @Override
    public WxPayDTO.RefundResponse queryRefund(String outRefundNo) {
        try {
            log.info("查询微信支付退款，退款单号: {}", outRefundNo);

            // TODO: 实现查询退款逻辑
            
            WxPayDTO.RefundResponse response = new WxPayDTO.RefundResponse();
            response.setOutRefundNo(outRefundNo);
            response.setStatus("SUCCESS");

            return response;

        } catch (Exception e) {
            log.error("查询微信支付退款失败", e);
            throw new ServiceException("查询退款失败: " + e.getMessage());
        }
    }

    @Override
    public boolean handlePayNotify(String notifyData) {
        try {
            log.info("处理微信支付回调通知: {}", notifyData);

            // 解析回调数据
            WxPayDTO.PayNotifyRequest notifyRequest = JSON.parseObject(notifyData, WxPayDTO.PayNotifyRequest.class);
            
            if (!"TRANSACTION.SUCCESS".equals(notifyRequest.getEventType())) {
                log.warn("非支付成功通知，忽略处理");
                return true;
            }

            // 解密通知数据
            String decryptedData = decryptNotifyData(
                    notifyRequest.getResource().getAssociatedData(),
                    notifyRequest.getResource().getNonce(),
                    notifyRequest.getResource().getCiphertext()
            );

            WxPayDTO.PaySuccessNotify payNotify = JSON.parseObject(decryptedData, WxPayDTO.PaySuccessNotify.class);
            
            if (!"SUCCESS".equals(payNotify.getTradeState())) {
                log.warn("支付状态非成功，状态: {}", payNotify.getTradeState());
                return true;
            }

            // 更新订单状态
            String orderNo = payNotify.getOutTradeNo();
            String transactionId = payNotify.getTransactionId();
            Date paymentTime = new Date();

            // 根据订单号前缀判断订单类型并更新相应订单状态
            if (orderNo.startsWith("COURSE_")) {
                courseOrderService.updateOrderPaymentStatus(orderNo, 1, "wechat", transactionId, paymentTime);
            } else if (orderNo.startsWith("MEDITATION_")) {
                meditationOrderService.updateOrderPaymentStatus(orderNo, 1, "wechat", transactionId, paymentTime);
            }
            // TODO: 添加其他订单类型的处理

            log.info("微信支付回调处理成功，订单号: {}, 交易号: {}", orderNo, transactionId);
            return true;

        } catch (Exception e) {
            log.error("处理微信支付回调通知失败", e);
            return false;
        }
    }

    @Override
    public boolean handleRefundNotify(String notifyData) {
        try {
            log.info("处理微信退款回调通知: {}", notifyData);

            // 解析回调数据
            WxPayDTO.PayNotifyRequest notifyRequest = JSON.parseObject(notifyData, WxPayDTO.PayNotifyRequest.class);

            if (!"REFUND.SUCCESS".equals(notifyRequest.getEventType())) {
                log.warn("非退款成功通知，忽略处理");
                return true;
            }

            // 解密通知数据
            String decryptedData = decryptNotifyData(
                    notifyRequest.getResource().getAssociatedData(),
                    notifyRequest.getResource().getNonce(),
                    notifyRequest.getResource().getCiphertext()
            );

            // TODO: 解析退款通知数据并更新订单状态
            log.info("退款通知解密数据: {}", decryptedData);

            return true;

        } catch (Exception e) {
            log.error("处理微信退款回调通知失败", e);
            return false;
        }
    }

    @Override
    public boolean verifyNotifySignature(String timestamp, String nonce, String body, String signature) {
        return wxPayUtils.verifyV3Signature(timestamp, nonce, body, signature);
    }

    @Override
    public boolean closeOrder(String outTradeNo) {
        try {
            log.info("关闭微信支付订单: {}", outTradeNo);

            // TODO: 实现关闭订单逻辑
            // V3版本关闭订单接口: POST /v3/pay/transactions/out-trade-no/{out_trade_no}/close

            return true;

        } catch (Exception e) {
            log.error("关闭微信支付订单失败", e);
            return false;
        }
    }

    @Override
    public String decryptNotifyData(String associatedData, String nonce, String ciphertext) {
        try {
            // 使用AES-256-GCM解密
            SecretKeySpec secretKeySpec = new SecretKeySpec(wxPayConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8), "AES");
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));
            
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
            cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));
            
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
            return new String(decryptedBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("解密微信支付回调数据失败", e);
            throw new ServiceException("解密回调数据失败: " + e.getMessage());
        }
    }
}
