package com.xihuan.common.utils.wechat;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.config.WxPayConfig;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.http.HttpUtils;
import com.xihuan.common.utils.sign.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;

/**
 * 微信支付工具类
 */
@Component
public class WxPayUtils {
    private static final Logger log = LoggerFactory.getLogger(WxPayUtils.class);

    @Autowired
    private WxPayConfig wxPayConfig;

    /**
     * 统一下单接口 - 小程序支付
     *
     * @param orderNo 订单号
     * @param totalFee 总金额（单位：分）
     * @param description 商品描述
     * @param openid 用户openid
     * @param ip 客户端IP
     * @return 支付参数，用于小程序调起支付
     */
    public Map<String, Object> createOrder(String orderNo, int totalFee, String description, String openid, String ip) {
        if (wxPayConfig.getUseV3()) {
            return createOrderV3(orderNo, totalFee, description, openid, ip);
        } else {
            return createOrderV2(orderNo, totalFee, description, openid, ip);
        }
    }

    /**
     * 统一下单接口 - V3版本
     */
    private Map<String, Object> createOrderV3(String orderNo, int totalFee, String description, String openid, String ip) {
        try {
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("appid", wxPayConfig.getAppId());
            requestData.put("mchid", wxPayConfig.getMchId());
            requestData.put("description", description);
            requestData.put("out_trade_no", orderNo);
            requestData.put("notify_url", wxPayConfig.getNotifyUrl());

            // 订单金额
            Map<String, Object> amount = new HashMap<>();
            amount.put("total", totalFee);
            amount.put("currency", "CNY");
            requestData.put("amount", amount);

            // 支付者
            Map<String, Object> payer = new HashMap<>();
            payer.put("openid", openid);
            requestData.put("payer", payer);

            // 附加数据
            Map<String, Object> scene_info = new HashMap<>();
            scene_info.put("payer_client_ip", ip);
            requestData.put("scene_info", scene_info);

            // 设置过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(wxPayConfig.getTimeoutMinutes());
            requestData.put("time_expire", expireTime.atZone(ZoneOffset.of("+08:00")).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));

            // 发送请求
            String url = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
            String requestBody = JSON.toJSONString(requestData);

            log.info("微信支付V3下单请求参数: {}", requestBody);

            // 构建请求头
            Map<String, String> headers = buildV3Headers("POST", "/v3/pay/transactions/jsapi", requestBody);

            // 发送HTTP请求
            String response = HttpUtils.sendPost(url, requestBody, headers.toString());
            log.info("微信支付V3下单响应: {}", response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("prepay_id")) {
                String prepayId = responseJson.getString("prepay_id");

                // 构建小程序调起支付的参数
                return buildMiniProgramPayParams(prepayId);
            } else {
                log.error("微信支付V3下单失败，响应: {}", response);
                throw new ServiceException("微信支付下单失败");
            }

        } catch (Exception e) {
            log.error("微信支付V3下单失败", e);
            throw new ServiceException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 统一下单接口 - V2版本
     */
    private Map<String, Object> createOrderV2(String orderNo, int totalFee, String description, String openid, String ip) {
        try {
            // 构建请求参数
            SortedMap<String, String> requestData = new TreeMap<>();
            requestData.put("appid", wxPayConfig.getAppId());
            requestData.put("mch_id", wxPayConfig.getMchId());
            requestData.put("nonce_str", generateNonceStr());
            requestData.put("body", description);
            requestData.put("out_trade_no", orderNo);
            requestData.put("total_fee", String.valueOf(totalFee));
            requestData.put("spbill_create_ip", ip);
            requestData.put("notify_url", wxPayConfig.getNotifyUrl());
            requestData.put("trade_type", "JSAPI");
            requestData.put("openid", openid);
            
            // 计算签名
            String sign = generateSignV2(requestData);
            requestData.put("sign", sign);
            
            // 将参数转换为XML
            String xmlData = mapToXml(requestData);
            
            // 发送请求
            String url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
            if (wxPayConfig.getUseSandbox()) {
                url = "https://api.mch.weixin.qq.com/sandboxnew/pay/unifiedorder";
            }
            
            log.info("微信支付V2下单请求参数: {}", xmlData);
            String result = HttpUtils.sendPost(url, xmlData, "application/xml");
            log.info("微信支付V2下单响应: {}", result);
            
            // 解析返回结果
            Map<String, String> responseMap = xmlToMap(result);
            
            if ("SUCCESS".equals(responseMap.get("return_code")) && "SUCCESS".equals(responseMap.get("result_code"))) {
                String prepayId = responseMap.get("prepay_id");
                
                // 构建小程序调起支付的参数
                Map<String, Object> payParams = new HashMap<>();
                payParams.put("appId", wxPayConfig.getAppId());
                payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                payParams.put("nonceStr", generateNonceStr());
                payParams.put("package", "prepay_id=" + prepayId);
                payParams.put("signType", "MD5");
                
                // 计算签名
                SortedMap<String, String> signParams = new TreeMap<>();
                signParams.put("appId", wxPayConfig.getAppId());
                signParams.put("timeStamp", payParams.get("timeStamp").toString());
                signParams.put("nonceStr", payParams.get("nonceStr").toString());
                signParams.put("package", payParams.get("package").toString());
                signParams.put("signType", "MD5");
                
                String paySign = generateSignV2(signParams);
                payParams.put("paySign", paySign);
                
                return payParams;
            } else {
                log.error("微信支付V2下单失败: {}", responseMap.get("return_msg"));
                throw new ServiceException("微信支付下单失败: " + responseMap.get("return_msg"));
            }
        } catch (Exception e) {
            log.error("微信支付V2下单失败", e);
            throw new ServiceException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 生成V2版本签名
     */
    private String generateSignV2(SortedMap<String, String> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (StringUtils.isNotEmpty(entry.getValue())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append("key=").append(wxPayConfig.getMchKey());
        return Md5Utils.hash(sb.toString()).toUpperCase();
    }

    /**
     * Map转XML
     */
    private String mapToXml(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            sb.append("<").append(entry.getKey()).append(">");
            sb.append(entry.getValue());
            sb.append("</").append(entry.getKey()).append(">");
        }
        sb.append("</xml>");
        return sb.toString();
    }

    /**
     * XML转Map
     */
    private Map<String, String> xmlToMap(String xml) {
        Map<String, String> map = new HashMap<>();
        // 简单解析，实际项目中建议使用XML解析库
        String[] elements = xml.replaceAll("</?xml>", "").split("</");
        for (String element : elements) {
            if (element.contains(">")) {
                String[] keyValue = element.split(">");
                if (keyValue.length >= 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    map.put(key, value);
                }
            }
        }
        return map;
    }

    /**
     * 构建V3版本请求头
     */
    private Map<String, String> buildV3Headers(String method, String url, String body) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("User-Agent", "XiHuan-Payment/1.0");

        // 构建Authorization头
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = generateNonceStr();
        String signature = generateV3Signature(method, url, timestamp, nonceStr, body);

        String authorization = String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
                wxPayConfig.getMchId(), nonceStr, timestamp, wxPayConfig.getCertSerialNo(), signature);

        headers.put("Authorization", authorization);

        return headers;
    }

    /**
     * 生成V3版本签名
     */
    private String generateV3Signature(String method, String url, String timestamp, String nonceStr, String body) throws Exception {
        // 构建待签名字符串
        String message = method + "\n" + url + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";

        // 使用私钥进行签名
        PrivateKey privateKey = loadPrivateKey();
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message.getBytes(StandardCharsets.UTF_8));

        return Base64.getEncoder().encodeToString(signature.sign());
    }

    /**
     * 加载私钥
     */
    private PrivateKey loadPrivateKey() throws Exception {
        String privateKeyContent;

        if (StringUtils.isNotEmpty(wxPayConfig.getPrivateKey())) {
            // 直接使用配置的私钥内容
            privateKeyContent = wxPayConfig.getPrivateKey();
        } else {
            // 从文件加载私钥
            ClassPathResource resource = new ClassPathResource(wxPayConfig.getPrivateKeyPath().replace("classpath:", ""));
            try (InputStream inputStream = resource.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                privateKeyContent = reader.lines().collect(Collectors.joining("\n"));
            }
        }

        // 清理私钥格式
        privateKeyContent = privateKeyContent
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 构建小程序调起支付参数
     */
    private Map<String, Object> buildMiniProgramPayParams(String prepayId) throws Exception {
        Map<String, Object> payParams = new HashMap<>();
        String appId = wxPayConfig.getAppId();
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = generateNonceStr();
        String packageValue = "prepay_id=" + prepayId;
        String signType = "RSA";

        payParams.put("appId", appId);
        payParams.put("timeStamp", timeStamp);
        payParams.put("nonceStr", nonceStr);
        payParams.put("package", packageValue);
        payParams.put("signType", signType);

        // 生成小程序支付签名
        String paySign = generateMiniProgramPaySign(appId, timeStamp, nonceStr, packageValue);
        payParams.put("paySign", paySign);

        return payParams;
    }

    /**
     * 生成小程序支付签名
     */
    private String generateMiniProgramPaySign(String appId, String timeStamp, String nonceStr, String packageValue) throws Exception {
        String message = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";

        PrivateKey privateKey = loadPrivateKey();
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message.getBytes(StandardCharsets.UTF_8));

        return Base64.getEncoder().encodeToString(signature.sign());
    }

    /**
     * 验证V3版本回调签名
     */
    public boolean verifyV3Signature(String timestamp, String nonce, String body, String signature) {
        try {
            // 构建待验证字符串
            String message = timestamp + "\n" + nonce + "\n" + body + "\n";

            // TODO: 这里需要使用微信支付平台证书来验证签名
            // 由于平台证书需要动态获取和管理，这里先返回true
            // 在生产环境中，需要实现完整的证书管理逻辑
            log.info("验证V3签名，待验证字符串: {}", message);

            return true;
        } catch (Exception e) {
            log.error("验证V3签名失败", e);
            return false;
        }
    }

    /**
     * 解密V3版本回调数据
     */
    public String decryptV3Data(String ciphertext, String associatedData, String nonce) throws Exception {
        byte[] key = wxPayConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8);
        byte[] nonceBytes = nonce.getBytes(StandardCharsets.UTF_8);
        byte[] ciphertextBytes = Base64.getDecoder().decode(ciphertext);
        byte[] associatedDataBytes = associatedData.getBytes(StandardCharsets.UTF_8);

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonceBytes);

        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
        cipher.updateAAD(associatedDataBytes);

        byte[] decryptedBytes = cipher.doFinal(ciphertextBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}
